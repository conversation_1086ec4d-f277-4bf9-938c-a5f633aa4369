console.log('Admin script loading...');

jQuery(document).ready(function($) {
    console.log('jQuery ready!');

    // Check if dakoii_ajax is available
    if (typeof dakoii_ajax === 'undefined') {
        console.error('Dakoii Slideshow: AJAX configuration not found');
        // Create a fallback object
        window.dakoii_ajax = {
            ajax_url: (typeof ajaxurl !== 'undefined' ? ajaxurl : '/wp-admin/admin-ajax.php'),
            admin_url: '/wp-admin/',
            nonce: ''
        };
        console.log('Using fallback AJAX configuration');
    } else {
        console.log('AJAX configuration loaded:', dakoii_ajax);
    }

    // Edit Group Handler
    $(document).on('click', '.edit-group', function(e) {
        e.preventDefault();
        console.log('Edit group clicked');
        var groupId = $(this).data('id');

        if (!groupId) {
            showMessage('Invalid group ID', 'error');
            return;
        }

        // Get group data
        $.post(dakoii_ajax.ajax_url, {
            action: 'dakoii_slideshow_action',
            slideshow_action: 'get_group',
            group_id: groupId,
            nonce: dakoii_ajax.nonce
        })
        .done(function(response) {
            if (response.success && response.data) {
                var group = response.data;
                $('#edit-group-id').val(group.id || '');
                $('#edit-group-name').val(group.name || '');
                $('#edit-group-description').val(group.description || '');
                $('#edit-group-tags').val(group.tags || '');
                $('#edit-group-modal').show();
            } else {
                showMessage('Error loading group data: ' + (response.data || 'Unknown error'), 'error');
            }
        })
        .fail(function(xhr, status, error) {
            console.error('AJAX Error:', status, error);
            showMessage('Error loading group data. Please try again.', 'error');
        });
    });

    // Manage Slides Handler
    $(document).on('click', '.manage-slides', function(e) {
        e.preventDefault();
        console.log('Manage slides clicked');
        var groupId = $(this).data('id');

        if (!groupId) {
            showMessage('Invalid group ID', 'error');
            return;
        }

        $('#slide-group-id').val(groupId);
        loadSlides(groupId);
        $('#manage-slides-modal').show();
    });

    // Delete Group Handler
    $(document).on('click', '.delete-group', function(e) {
        e.preventDefault();
        console.log('Delete group clicked');

        if (!confirm('Are you sure you want to delete this slideshow group? All slides will be deleted as well.')) {
            return;
        }

        var groupId = $(this).data('id');

        if (!groupId) {
            showMessage('Invalid group ID', 'error');
            return;
        }

        $.post(dakoii_ajax.ajax_url, {
            action: 'dakoii_slideshow_action',
            slideshow_action: 'delete_group',
            group_id: groupId,
            nonce: dakoii_ajax.nonce
        })
        .done(function(response) {
            if (response.success) {
                showMessage('Group deleted successfully!', 'success');
                setTimeout(function() {
                    location.reload();
                }, 1000);
            } else {
                showMessage('Error deleting group: ' + (response.data || 'Unknown error'), 'error');
            }
        })
        .fail(function(xhr, status, error) {
            console.error('AJAX Error:', status, error);
            showMessage('Error deleting group. Please try again.', 'error');
        });
    });

    // Modal Close Handlers
    $('#cancel-edit, #cancel-slides').on('click', function() {
        $(this).closest('[id$="-modal"]').hide();
    });

    // Close modal when clicking outside
    $(document).on('click', '[id$="-modal"]', function(e) {
        if (e.target === this) {
            $(this).hide();
        }
    });

    // Update Group Form Handler
    $('#edit-group-form').on('submit', function(e) {
        e.preventDefault();

        var formData = {
            action: 'dakoii_slideshow_action',
            slideshow_action: 'update_group',
            group_id: $('#edit-group-id').val(),
            name: $('#edit-group-name').val(),
            description: $('#edit-group-description').val(),
            tags: $('#edit-group-tags').val(),
            nonce: dakoii_ajax.nonce
        };

        $.post(dakoii_ajax.ajax_url, formData, function(response) {
            if (response.success) {
                showMessage('Group updated successfully!', 'success');
                $('#edit-group-modal').hide();
                location.reload();
            } else {
                showMessage('Error updating group: ' + response.data, 'error');
            }
        });
    });

    // Add Group Form Handler (for the add new group page)
    $('#add-group-form').on('submit', function(e) {
        e.preventDefault();
        console.log('Add group form submitted');

        var formData = {
            action: 'dakoii_slideshow_action',
            slideshow_action: 'create_group',
            name: $('#group-name').val(),
            description: $('#group-description').val(),
            tags: $('#group-tags').val(),
            nonce: dakoii_ajax.nonce
        };

        $.post(dakoii_ajax.ajax_url, formData)
            .done(function(response) {
                if (response.success) {
                    showMessage('Group created successfully!', 'success');
                    $('#add-group-form')[0].reset();
                    setTimeout(function() {
                        window.location.href = dakoii_ajax.admin_url + 'admin.php?page=dakoii-slideshows';
                    }, 1000);
                } else {
                    showMessage('Error creating group: ' + (response.data || 'Unknown error'), 'error');
                }
            })
            .fail(function(xhr, status, error) {
                console.error('AJAX Error:', status, error);
                showMessage('Error creating group. Please try again.', 'error');
            });
    });

    // Helper Functions
    function loadSlides(groupId) {
        if (!groupId) {
            $('#slides-container').html('<p>Invalid group ID</p>');
            return;
        }

        $.post(dakoii_ajax.ajax_url, {
            action: 'dakoii_slideshow_action',
            slideshow_action: 'get_slides',
            group_id: groupId,
            nonce: dakoii_ajax.nonce
        })
        .done(function(response) {
            if (response.success) {
                displaySlides(response.data || []);
            } else {
                $('#slides-container').html('<p>Error loading slides: ' + (response.data || 'Unknown error') + '</p>');
            }
        })
        .fail(function(xhr, status, error) {
            console.error('AJAX Error:', status, error);
            $('#slides-container').html('<p>Error loading slides. Please try again.</p>');
        });
    }

    function displaySlides(slides) {
        var container = $('#slides-container');
        container.empty();

        if (!slides || slides.length === 0) {
            container.html('<p>No slides found. Add your first slide below.</p>');
            return;
        }

        slides.forEach(function(slide) {
            var slideHtml = '<div class="slide-item">' +
                '<div class="slide-preview">' +
                    '<img src="' + (slide.image_url || '') + '" alt="' + (slide.title || 'Slide') + '" style="width:80px;height:60px;object-fit:cover;">' +
                    '<div class="slide-info">' +
                        '<div class="slide-title"><strong>' + (slide.title || 'Untitled') + '</strong></div>' +
                        '<div class="slide-description">' + (slide.description || '') + '</div>' +
                        '<div class="slide-order">Order: ' + (slide.slide_order || 0) + '</div>' +
                    '</div>' +
                    '<div class="slide-actions">' +
                        '<a href="#" class="delete-slide" data-id="' + (slide.id || '') + '">Delete</a>' +
                    '</div>' +
                '</div>' +
            '</div>';

            container.append(slideHtml);
        });
    }

    function showMessage(message, type) {
        var messageClass = type === 'success' ? 'notice-success' : 'notice-error';
        var messageHtml = '<div class="notice ' + messageClass + ' is-dismissible"><p>' + message + '</p></div>';

        $('#dakoii-message').html(messageHtml);

        // Auto-hide success messages
        if (type === 'success') {
            setTimeout(function() {
                $('#dakoii-message').fadeOut();
            }, 3000);
        }
    }

    console.log('Handlers registered!');
});