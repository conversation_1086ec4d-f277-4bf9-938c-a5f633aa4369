jQuery(document).ready(function($) {
    console.log('Dakoii Slideshow: JavaScript loaded');

    // Check if dakoii_ajax is available, but don't exit completely
    if (typeof dakoii_ajax === 'undefined') {
        console.error('Dakoii Slideshow: AJAX configuration not found');
        // Create a fallback object to prevent errors
        window.dakoii_ajax = {
            ajax_url: (typeof ajaxurl !== 'undefined' ? ajaxurl : '/wp-admin/admin-ajax.php'),
            admin_url: '/wp-admin/',
            nonce: ''
        };
        console.log('Dakoii Slideshow: Using fallback AJAX configuration');
    } else {
        console.log('Dakoii Slideshow: AJAX configuration loaded successfully');
    }
    
    // Add Group Form Handler
    $('#add-group-form').on('submit', function(e) {
        e.preventDefault();
        
        var formData = {
            action: 'dakoii_slideshow_action',
            slideshow_action: 'create_group',
            name: $('#group-name').val(),
            description: $('#group-description').val(),
            tags: $('#group-tags').val(),
            nonce: dakoii_ajax.nonce
        };
        
        $.post(dakoii_ajax.ajax_url, formData)
            .done(function(response) {
                if (response.success) {
                    showMessage('Group created successfully!', 'success');
                    $('#add-group-form')[0].reset();
                    setTimeout(function() {
                        window.location.href = dakoii_ajax.admin_url + 'admin.php?page=dakoii-slideshows';
                    }, 1000);
                } else {
                    showMessage('Error creating group: ' + (response.data || 'Unknown error'), 'error');
                }
            })
            .fail(function(xhr, status, error) {
                console.error('AJAX Error:', status, error);
                showMessage('Error creating group. Please try again.', 'error');
            });
    });
    
    // Edit Group Handler
    $(document).on('click', '.edit-group', function(e) {
        e.preventDefault();
        var groupId = $(this).data('id');
        
        if (!groupId) {
            showMessage('Invalid group ID', 'error');
            return;
        }
        
        // Get group data
        $.post(dakoii_ajax.ajax_url, {
            action: 'dakoii_slideshow_action',
            slideshow_action: 'get_group',
            group_id: groupId,
            nonce: dakoii_ajax.nonce
        })
        .done(function(response) {
            if (response.success && response.data) {
                var group = response.data;
                $('#edit-group-id').val(group.id || '');
                $('#edit-group-name').val(group.name || '');
                $('#edit-group-description').val(group.description || '');
                $('#edit-group-tags').val(group.tags || '');
                $('#edit-group-modal').show();
            } else {
                showMessage('Error loading group data: ' + (response.data || 'Unknown error'), 'error');
            }
        })
        .fail(function(xhr, status, error) {
            console.error('AJAX Error:', status, error);
            showMessage('Error loading group data. Please try again.', 'error');
        });
    });
    
    // Update Group Form Handler
    $('#edit-group-form').on('submit', function(e) {
        e.preventDefault();
        
        var formData = {
            action: 'dakoii_slideshow_action',
            slideshow_action: 'update_group',
            group_id: $('#edit-group-id').val(),
            name: $('#edit-group-name').val(),
            description: $('#edit-group-description').val(),
            tags: $('#edit-group-tags').val(),
            nonce: dakoii_ajax.nonce
        };
        
        $.post(dakoii_ajax.ajax_url, formData, function(response) {
            if (response.success) {
                showMessage('Group updated successfully!', 'success');
                $('#edit-group-modal').hide();
                location.reload();
            } else {
                showMessage('Error updating group: ' + response.data, 'error');
            }
        });
    });
    
    // Delete Group Handler
    $(document).on('click', '.delete-group', function(e) {
        e.preventDefault();
        
        if (!confirm('Are you sure you want to delete this slideshow group? All slides will be deleted as well.')) {
            return;
        }
        
        var groupId = $(this).data('id');
        
        if (!groupId) {
            showMessage('Invalid group ID', 'error');
            return;
        }
        
        $.post(dakoii_ajax.ajax_url, {
            action: 'dakoii_slideshow_action',
            slideshow_action: 'delete_group',
            group_id: groupId,
            nonce: dakoii_ajax.nonce
        })
        .done(function(response) {
            if (response.success) {
                showMessage('Group deleted successfully!', 'success');
                setTimeout(function() {
                    location.reload();
                }, 1000);
            } else {
                showMessage('Error deleting group: ' + (response.data || 'Unknown error'), 'error');
            }
        })
        .fail(function(xhr, status, error) {
            console.error('AJAX Error:', status, error);
            showMessage('Error deleting group. Please try again.', 'error');
        });
    });
    
    // Manage Slides Handler
    $(document).on('click', '.manage-slides', function(e) {
        e.preventDefault();
        var groupId = $(this).data('id');
        
        if (!groupId) {
            showMessage('Invalid group ID', 'error');
            return;
        }
        
        $('#slide-group-id').val(groupId);
        loadSlides(groupId);
        $('#manage-slides-modal').show();
    });
    
    // Add Slide Form Handler
    $('#add-slide-form').on('submit', function(e) {
        e.preventDefault();
        
        var formData = {
            action: 'dakoii_slideshow_action',
            slideshow_action: 'add_slide',
            group_id: $('#slide-group-id').val(),
            title: $('#slide-title').val(),
            description: $('#slide-description').val(),
            image_url: $('#slide-image').val(),
            link_url: $('#slide-link').val(),
            order: $('#slide-order').val(),
            nonce: dakoii_ajax.nonce
        };
        
        $.post(dakoii_ajax.ajax_url, formData, function(response) {
            if (response.success) {
                showMessage('Slide added successfully!', 'success');
                $('#add-slide-form')[0].reset();
                loadSlides($('#slide-group-id').val());
            } else {
                showMessage('Error adding slide: ' + response.data, 'error');
            }
        });
    });
    
    // Media Uploader for Images
    $(document).on('click', '#select-image', function(e) {
        e.preventDefault();
        
        // Check if wp.media is available
        if (typeof wp === 'undefined' || typeof wp.media === 'undefined') {
            showMessage('WordPress media library not available', 'error');
            return;
        }
        
        var mediaUploader = wp.media({
            title: 'Select Image',
            button: {
                text: 'Use This Image'
            },
            multiple: false
        });
        
        mediaUploader.on('select', function() {
            try {
                var attachment = mediaUploader.state().get('selection').first().toJSON();
                if (attachment && attachment.url) {
                    $('#slide-image').val(attachment.url);
                } else {
                    showMessage('Invalid image selected', 'error');
                }
            } catch (error) {
                console.error('Media selection error:', error);
                showMessage('Error selecting image', 'error');
            }
        });
        
        try {
            mediaUploader.open();
        } catch (error) {
            console.error('Media uploader error:', error);
            showMessage('Error opening media library', 'error');
        }
    });
    
    // Modal Close Handlers
    $('#cancel-edit, #cancel-slides').on('click', function() {
        $(this).closest('[id$="-modal"]').hide();
    });
    
    // Close modal when clicking outside
    $(document).on('click', '[id$="-modal"]', function(e) {
        if (e.target === this) {
            $(this).hide();
        }
    });
    
    // Delete Slide Handler (delegated)
    $(document).on('click', '.delete-slide', function(e) {
        e.preventDefault();
        
        if (!confirm('Are you sure you want to delete this slide?')) {
            return;
        }
        
        var slideId = $(this).data('id');
        
        $.post(dakoii_ajax.ajax_url, {
            action: 'dakoii_slideshow_action',
            slideshow_action: 'delete_slide',
            slide_id: slideId,
            nonce: dakoii_ajax.nonce
        }, function(response) {
            if (response.success) {
                showMessage('Slide deleted successfully!', 'success');
                loadSlides($('#slide-group-id').val());
            } else {
                showMessage('Error deleting slide: ' + response.data, 'error');
            }
        });
    });
    
    // Helper Functions
    function loadSlides(groupId) {
        if (!groupId) {
            $('#slides-container').html('<p>Invalid group ID</p>');
            return;
        }
        
        $.post(dakoii_ajax.ajax_url, {
            action: 'dakoii_slideshow_action',
            slideshow_action: 'get_slides',
            group_id: groupId,
            nonce: dakoii_ajax.nonce
        })
        .done(function(response) {
            if (response.success) {
                displaySlides(response.data || []);
            } else {
                $('#slides-container').html('<p>Error loading slides: ' + (response.data || 'Unknown error') + '</p>');
            }
        })
        .fail(function(xhr, status, error) {
            console.error('AJAX Error:', status, error);
            $('#slides-container').html('<p>Error loading slides. Please try again.</p>');
        });
    }
    
    function displaySlides(slides) {
        var container = $('#slides-container');
        container.empty();
        
        if (!slides || slides.length === 0) {
            container.html('<p>No slides found. Add your first slide below.</p>');
            return;
        }
        
        try {
            slides.forEach(function(slide) {
                var slideHtml = '<div class="slide-item">' +
                    '<div class="slide-preview">' +
                        '<img src="' + (slide.image_url || '') + '" alt="' + (slide.title || 'Slide') + '" onerror="this.src=\'data:image/svg+xml;base64,PHN2ZyB3aWR0aD1cIjgwXCIgaGVpZ2h0PVwiNjBcIiB2aWV3Qm94PVwiMCAwIDgwIDYwXCIgZmlsbD1cIm5vbmVcIiB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCI+CiAgPHJlY3Qgd2lkdGg9XCI4MFwiIGhlaWdodD1cIjYwXCIgZmlsbD1cIiNmNWY1ZjVcIi8+CiAgPHRleHQgeD1cIjQwXCIgeT1cIjMwXCIgdGV4dC1hbmNob3I9XCJtaWRkbGVcIiBkeT1cIi4zZW1cIiBmaWxsPVwiIzk5OVwiIGZvbnQtZmFtaWx5PVwiQXJpYWwsIHNhbnMtc2VyaWZcIiBmb250LXNpemU9XCIxMlwiPk5vIEltYWdlPC90ZXh0Pgo8L3N2Zz4K\'">' +
                        '<div class="slide-info">' +
                            '<div class="slide-title">' + (slide.title || 'Untitled') + '</div>' +
                            '<div class="slide-description">' + (slide.description || '') + '</div>' +
                            '<div class="slide-order">Order: ' + (slide.slide_order || 0) + '</div>' +
                        '</div>' +
                        '<div class="slide-actions">' +
                            '<a href="#" class="delete-slide" data-id="' + (slide.id || '') + '">Delete</a>' +
                        '</div>' +
                    '</div>' +
                '</div>';
                
                container.append(slideHtml);
            });
        } catch (error) {
            console.error('Error displaying slides:', error);
            container.html('<p>Error displaying slides. Please refresh and try again.</p>');
        }
    }
    
    function showMessage(message, type) {
        var messageClass = type === 'success' ? 'notice-success' : 'notice-error';
        var messageHtml = '<div class="notice ' + messageClass + ' is-dismissible"><p>' + message + '</p></div>';
        
        $('#dakoii-message').html(messageHtml);
        
        // Auto-hide success messages
        if (type === 'success') {
            setTimeout(function() {
                $('#dakoii-message').fadeOut();
            }, 3000);
        }
    }
});