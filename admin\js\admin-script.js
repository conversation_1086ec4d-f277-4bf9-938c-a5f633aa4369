jQuery(document).ready(function($) {
    // Check if dakoii_ajax is available
    if (typeof dakoii_ajax === 'undefined') {
        // Create a fallback object
        window.dakoii_ajax = {
            ajax_url: (typeof ajaxurl !== 'undefined' ? ajaxurl : '/wp-admin/admin-ajax.php'),
            admin_url: '/wp-admin/',
            nonce: ''
        };
    }

    // Delete Group Handler (AJAX)
    $(document).on('click', '.delete-group', function(e) {
        e.preventDefault();

        var groupId = $(this).data('id');

        if (!groupId) {
            alert('Invalid group ID');
            return;
        }

        $.post(dakoii_ajax.ajax_url, {
            action: 'dakoii_slideshow_action',
            slideshow_action: 'delete_group',
            group_id: groupId,
            nonce: dakoii_ajax.nonce
        })
        .done(function(response) {
            if (response.success) {
                alert('Group deleted successfully!');
                location.reload();
            } else {
                alert('Error deleting group: ' + (response.data || 'Unknown error'));
            }
        })
        .fail(function(xhr, status, error) {
            alert('Error deleting group. Please try again.');
        });
    });

    // Media Uploader for Images
    $(document).on('click', '#select-image', function(e) {
        e.preventDefault();

        // Check if wp.media is available
        if (typeof wp === 'undefined' || typeof wp.media === 'undefined') {
            alert('WordPress media library not available');
            return;
        }

        var mediaUploader = wp.media({
            title: 'Select Image',
            button: {
                text: 'Use This Image'
            },
            multiple: false
        });

        mediaUploader.on('select', function() {
            try {
                var attachment = mediaUploader.state().get('selection').first().toJSON();
                if (attachment && attachment.url) {
                    $('#image_url').val(attachment.url);
                } else {
                    alert('Invalid image selected');
                }
            } catch (error) {
                alert('Error selecting image');
            }
        });

        try {
            mediaUploader.open();
        } catch (error) {
            alert('Error opening media library');
        }
    });
});