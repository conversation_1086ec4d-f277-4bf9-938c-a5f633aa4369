<?php
class DakoiiSlideshowAdmin {
    
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('wp_ajax_dakoii_slideshow_action', array($this, 'handle_ajax_requests'));
    }
    
    public function add_admin_menu() {
        add_menu_page(
            'Dakoii Slideshows',
            'Slideshows',
            'manage_options',
            'dakoii-slideshows',
            array($this, 'admin_page'),
            'dashicons-images-alt2',
            30
        );
        
        add_submenu_page(
            'dakoii-slideshows',
            'All Slideshow Groups',
            'All Groups',
            'manage_options',
            'dakoii-slideshows',
            array($this, 'admin_page')
        );
        
        add_submenu_page(
            'dakoii-slideshows',
            'Add New Group',
            'Add New Group',
            'manage_options',
            'dakoii-slideshows-add',
            array($this, 'add_group_page')
        );
    }
    
    public function enqueue_admin_scripts($hook) {
        // Debug: Log the hook to see if it matches
        error_log('Dakoii Slideshow: Hook = ' . $hook);

        if (strpos($hook, 'dakoii-slideshows') !== false) {
            error_log('Dakoii Slideshow: Enqueuing scripts for ' . $hook);

            wp_enqueue_media();
            wp_enqueue_script(
                'dakoii-admin-js',
                DAKOII_SLIDESHOW_PLUGIN_URL . 'admin/js/admin-script.js',
                array('jquery'),
                DAKOII_SLIDESHOW_VERSION,
                true // Load in footer
            );
            wp_enqueue_style(
                'dakoii-admin-css',
                DAKOII_SLIDESHOW_PLUGIN_URL . 'admin/css/admin-style.css',
                array(),
                DAKOII_SLIDESHOW_VERSION
            );

            wp_localize_script('dakoii-admin-js', 'dakoii_ajax', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'admin_url' => admin_url(),
                'nonce' => wp_create_nonce('dakoii_slideshow_nonce')
            ));

            error_log('Dakoii Slideshow: Scripts enqueued successfully');
        }
    }
    
    public function admin_page() {
        $groups = DakoiiSlideshow::get_groups();
        ?>
        <div class="wrap">
            <h1>Slideshow Groups <a href="<?php echo admin_url('admin.php?page=dakoii-slideshows-add'); ?>" class="page-title-action">Add New</a></h1>
            
            <div id="dakoii-message"></div>
            
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Description</th>
                        <th>Tags</th>
                        <th>Slides</th>
                        <th>Shortcode</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if ($groups): ?>
                        <?php foreach ($groups as $group): ?>
                            <?php $slides = DakoiiSlideshow::get_slides($group->id); ?>
                            <tr>
                                <td><strong><?php echo esc_html($group->name); ?></strong></td>
                                <td><?php echo esc_html($group->description); ?></td>
                                <td><?php echo esc_html($group->tags); ?></td>
                                <td><?php echo count($slides); ?></td>
                                <td><code>[dakoii_slideshow id="<?php echo $group->id; ?>"]</code></td>
                                <td>
                                    <a href="#" class="edit-group" data-id="<?php echo $group->id; ?>">Edit</a> |
                                    <a href="#" class="manage-slides" data-id="<?php echo $group->id; ?>">Manage Slides</a> |
                                    <a href="#" class="delete-group" data-id="<?php echo $group->id; ?>" style="color: #a00;">Delete</a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="6">No slideshow groups found. <a href="<?php echo admin_url('admin.php?page=dakoii-slideshows-add'); ?>">Create your first one</a>.</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
        
        <!-- Edit Group Modal -->
        <div id="edit-group-modal" style="display: none;">
            <div class="modal-content">
                <h3>Edit Slideshow Group</h3>
                <form id="edit-group-form">
                    <input type="hidden" id="edit-group-id" name="group_id">
                    <table class="form-table">
                        <tr>
                            <th><label for="edit-group-name">Group Name</label></th>
                            <td><input type="text" id="edit-group-name" name="name" required class="regular-text"></td>
                        </tr>
                        <tr>
                            <th><label for="edit-group-description">Description</label></th>
                            <td><textarea id="edit-group-description" name="description" class="large-text" rows="3"></textarea></td>
                        </tr>
                        <tr>
                            <th><label for="edit-group-tags">Tags</label></th>
                            <td><input type="text" id="edit-group-tags" name="tags" class="regular-text" placeholder="home, about, contact"></td>
                        </tr>
                    </table>
                    <p class="submit">
                        <input type="submit" class="button-primary" value="Update Group">
                        <button type="button" class="button" id="cancel-edit">Cancel</button>
                    </p>
                </form>
            </div>
        </div>
        
        <!-- Manage Slides Modal -->
        <div id="manage-slides-modal" style="display: none;">
            <div class="modal-content large">
                <h3>Manage Slides</h3>
                <div id="slides-container"></div>
                <hr>
                <h4>Add New Slide</h4>
                <form id="add-slide-form">
                    <input type="hidden" id="slide-group-id" name="group_id">
                    <table class="form-table">
                        <tr>
                            <th><label for="slide-title">Title</label></th>
                            <td><input type="text" id="slide-title" name="title" class="regular-text"></td>
                        </tr>
                        <tr>
                            <th><label for="slide-description">Description</label></th>
                            <td><textarea id="slide-description" name="description" class="large-text" rows="3"></textarea></td>
                        </tr>
                        <tr>
                            <th><label for="slide-image">Image</label></th>
                            <td>
                                <input type="url" id="slide-image" name="image_url" class="regular-text" required>
                                <button type="button" class="button" id="select-image">Select Image</button>
                            </td>
                        </tr>
                        <tr>
                            <th><label for="slide-link">Link URL</label></th>
                            <td><input type="url" id="slide-link" name="link_url" class="regular-text"></td>
                        </tr>
                        <tr>
                            <th><label for="slide-order">Order</label></th>
                            <td><input type="number" id="slide-order" name="order" class="small-text" value="0"></td>
                        </tr>
                    </table>
                    <p class="submit">
                        <input type="submit" class="button-primary" value="Add Slide">
                        <button type="button" class="button" id="cancel-slides">Close</button>
                    </p>
                </form>
            </div>
        </div>
        <?php
    }
    
    public function add_group_page() {
        ?>
        <div class="wrap">
            <h1>Add New Slideshow Group</h1>
            
            <div id="dakoii-message"></div>
            
            <form id="add-group-form">
                <table class="form-table">
                    <tr>
                        <th><label for="group-name">Group Name</label></th>
                        <td><input type="text" id="group-name" name="name" required class="regular-text"></td>
                    </tr>
                    <tr>
                        <th><label for="group-description">Description</label></th>
                        <td><textarea id="group-description" name="description" class="large-text" rows="5"></textarea></td>
                    </tr>
                    <tr>
                        <th><label for="group-tags">Tags</label></th>
                        <td>
                            <input type="text" id="group-tags" name="tags" class="regular-text" placeholder="home, about, contact">
                            <p class="description">Comma-separated tags to identify where this slideshow should appear.</p>
                        </td>
                    </tr>
                </table>
                
                <p class="submit">
                    <input type="submit" class="button-primary" value="Create Group">
                    <a href="<?php echo admin_url('admin.php?page=dakoii-slideshows'); ?>" class="button">Cancel</a>
                </p>
            </form>
        </div>
        <?php
    }
    
    public function handle_ajax_requests() {
        // Verify nonce first
        if (!check_ajax_referer('dakoii_slideshow_nonce', 'nonce', false)) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Unauthorized access');
            return;
        }
        
        if (!isset($_POST['slideshow_action'])) {
            wp_send_json_error('No action specified');
            return;
        }
        
        $action = sanitize_text_field($_POST['slideshow_action']);
        
        try {
            switch ($action) {
                case 'create_group':
                    if (empty($_POST['name'])) {
                        wp_send_json_error('Group name is required');
                        return;
                    }
                    $result = DakoiiSlideshow::create_group(
                        $_POST['name'],
                        $_POST['description'] ?? '',
                        $_POST['tags'] ?? ''
                    );
                    if ($result !== false) {
                        wp_send_json_success('Group created successfully');
                    } else {
                        wp_send_json_error('Failed to create group');
                    }
                    break;
                    
                case 'update_group':
                    if (empty($_POST['group_id']) || empty($_POST['name'])) {
                        wp_send_json_error('Group ID and name are required');
                        return;
                    }
                    $result = DakoiiSlideshow::update_group(
                        intval($_POST['group_id']),
                        $_POST['name'],
                        $_POST['description'] ?? '',
                        $_POST['tags'] ?? ''
                    );
                    if ($result !== false) {
                        wp_send_json_success('Group updated successfully');
                    } else {
                        wp_send_json_error('Failed to update group');
                    }
                    break;
                    
                case 'delete_group':
                    if (empty($_POST['group_id'])) {
                        wp_send_json_error('Group ID is required');
                        return;
                    }
                    $result = DakoiiSlideshow::delete_group(intval($_POST['group_id']));
                    if ($result !== false) {
                        wp_send_json_success('Group deleted successfully');
                    } else {
                        wp_send_json_error('Failed to delete group');
                    }
                    break;
                    
                case 'get_group':
                    if (empty($_POST['group_id'])) {
                        wp_send_json_error('Group ID is required');
                        return;
                    }
                    $group = DakoiiSlideshow::get_group(intval($_POST['group_id']));
                    if ($group) {
                        wp_send_json_success($group);
                    } else {
                        wp_send_json_error('Group not found');
                    }
                    break;
                    
                case 'add_slide':
                    if (empty($_POST['group_id']) || empty($_POST['image_url'])) {
                        wp_send_json_error('Group ID and image URL are required');
                        return;
                    }
                    $result = DakoiiSlideshow::add_slide(
                        intval($_POST['group_id']),
                        $_POST['title'] ?? '',
                        $_POST['description'] ?? '',
                        $_POST['image_url'],
                        $_POST['link_url'] ?? '',
                        intval($_POST['order'] ?? 0)
                    );
                    if ($result !== false) {
                        wp_send_json_success('Slide added successfully');
                    } else {
                        wp_send_json_error('Failed to add slide');
                    }
                    break;
                    
                case 'get_slides':
                    if (empty($_POST['group_id'])) {
                        wp_send_json_error('Group ID is required');
                        return;
                    }
                    $slides = DakoiiSlideshow::get_slides(intval($_POST['group_id']));
                    wp_send_json_success($slides);
                    break;
                    
                case 'delete_slide':
                    if (empty($_POST['slide_id'])) {
                        wp_send_json_error('Slide ID is required');
                        return;
                    }
                    $result = DakoiiSlideshow::delete_slide(intval($_POST['slide_id']));
                    if ($result !== false) {
                        wp_send_json_success('Slide deleted successfully');
                    } else {
                        wp_send_json_error('Failed to delete slide');
                    }
                    break;
                    
                default:
                    wp_send_json_error('Invalid action: ' . $action);
            }
        } catch (Exception $e) {
            error_log('Dakoii Slideshow Error: ' . $e->getMessage());
            wp_send_json_error('An error occurred. Please try again.');
        }
        
        wp_die();
    }
}