<?php
class DakoiiSlideshowAdmin {
    
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('wp_ajax_dakoii_slideshow_action', array($this, 'handle_ajax_requests'));
        add_action('admin_notices', array($this, 'debug_admin_notice'));
    }

    public function debug_admin_notice() {
        // Remove debug notice since plugin is working
        // if (isset($_GET['page']) && strpos($_GET['page'], 'dakoii-slideshows') !== false) {
        //     echo '<div class="notice notice-success"><p>Dakoii Slideshow Plugin is active and working!</p></div>';
        // }
    }
    
    public function add_admin_menu() {
        add_menu_page(
            'Dakoii Slideshows',
            'Slideshows',
            'manage_options',
            'dakoii-slideshows',
            array($this, 'admin_page'),
            'dashicons-images-alt2',
            30
        );
        
        add_submenu_page(
            'dakoii-slideshows',
            'All Slideshow Groups',
            'All Groups',
            'manage_options',
            'dakoii-slideshows',
            array($this, 'admin_page')
        );
        
        add_submenu_page(
            'dakoii-slideshows',
            'Add New Group',
            'Add New Group',
            'manage_options',
            'dakoii-slideshows-add',
            array($this, 'add_group_page')
        );

        // Hidden submenu pages (don't show in menu)
        add_submenu_page(
            null, // No parent menu (hidden)
            'Edit Slideshow Group',
            'Edit Group',
            'manage_options',
            'dakoii-slideshows-edit',
            array($this, 'edit_group_page')
        );

        add_submenu_page(
            null, // No parent menu (hidden)
            'Manage Slides',
            'Manage Slides',
            'manage_options',
            'dakoii-slideshows-manage',
            array($this, 'manage_slides_page')
        );
    }
    
    public function enqueue_admin_scripts($hook) {
        // Debug: Log the hook to see if it matches
        error_log('Dakoii Slideshow: Hook = ' . $hook);

        // Load scripts on slideshow admin pages
        if (strpos($hook, 'dakoii-slideshows') !== false || strpos($hook, 'slideshows') !== false) {
            error_log('Dakoii Slideshow: Enqueuing scripts for ' . $hook);

            wp_enqueue_media();
            $script_url = DAKOII_SLIDESHOW_PLUGIN_URL . 'admin/js/admin-script.js';
            $test_script_url = DAKOII_SLIDESHOW_PLUGIN_URL . 'test-script.js';
            error_log('Dakoii Slideshow: Script URL = ' . $script_url);
            error_log('Dakoii Slideshow: Test Script URL = ' . $test_script_url);

            // Test script removed - main script is working

            wp_enqueue_script(
                'dakoii-admin-js',
                $script_url,
                array('jquery'),
                DAKOII_SLIDESHOW_VERSION,
                true // Load in footer
            );
            wp_enqueue_style(
                'dakoii-admin-css',
                DAKOII_SLIDESHOW_PLUGIN_URL . 'admin/css/admin-style.css',
                array(),
                DAKOII_SLIDESHOW_VERSION
            );

            wp_localize_script('dakoii-admin-js', 'dakoii_ajax', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'admin_url' => admin_url(),
                'nonce' => wp_create_nonce('dakoii_slideshow_nonce')
            ));

            error_log('Dakoii Slideshow: Scripts enqueued successfully');
        }
    }
    
    public function admin_page() {
        $groups = DakoiiSlideshow::get_groups();
        ?>
        <div class="wrap">
            <h1>Slideshow Groups <a href="<?php echo admin_url('admin.php?page=dakoii-slideshows-add'); ?>" class="page-title-action">Add New</a></h1>
            
            <div id="dakoii-message"></div>
            
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Description</th>
                        <th>Tags</th>
                        <th>Slides</th>
                        <th>Shortcode</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if ($groups): ?>
                        <?php foreach ($groups as $group): ?>
                            <?php $slides = DakoiiSlideshow::get_slides($group->id); ?>
                            <tr>
                                <td><strong><?php echo esc_html($group->name); ?></strong></td>
                                <td><?php echo esc_html($group->description); ?></td>
                                <td><?php echo esc_html($group->tags); ?></td>
                                <td><?php echo count($slides); ?></td>
                                <td><code>[dakoii_slideshow id="<?php echo $group->id; ?>"]</code></td>
                                <td>
                                    <a href="<?php echo admin_url('admin.php?page=dakoii-slideshows-edit&id=' . $group->id); ?>">Edit</a> |
                                    <a href="<?php echo admin_url('admin.php?page=dakoii-slideshows-manage&id=' . $group->id); ?>">Manage Slides</a> |
                                    <a href="#" class="delete-group" data-id="<?php echo $group->id; ?>" style="color: #a00;" onclick="return confirm('Are you sure you want to delete this slideshow group? All slides will be deleted as well.');">Delete</a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="6">No slideshow groups found. <a href="<?php echo admin_url('admin.php?page=dakoii-slideshows-add'); ?>">Create your first one</a>.</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
        <?php
    }
    
    public function add_group_page() {
        ?>
        <div class="wrap">
            <h1>Add New Slideshow Group</h1>
            
            <div id="dakoii-message"></div>
            
            <form id="add-group-form" action="javascript:void(0)">
                <table class="form-table">
                    <tr>
                        <th><label for="group-name">Group Name</label></th>
                        <td><input type="text" id="group-name" name="name" required class="regular-text"></td>
                    </tr>
                    <tr>
                        <th><label for="group-description">Description</label></th>
                        <td><textarea id="group-description" name="description" class="large-text" rows="5"></textarea></td>
                    </tr>
                    <tr>
                        <th><label for="group-tags">Tags</label></th>
                        <td>
                            <input type="text" id="group-tags" name="tags" class="regular-text" placeholder="home, about, contact">
                            <p class="description">Comma-separated tags to identify where this slideshow should appear.</p>
                        </td>
                    </tr>
                </table>
                
                <p class="submit">
                    <input type="submit" class="button-primary" value="Create Group">
                    <a href="<?php echo admin_url('admin.php?page=dakoii-slideshows'); ?>" class="button">Cancel</a>
                </p>
            </form>
        </div>
        <?php
    }

    public function edit_group_page() {
        $group_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

        if (!$group_id) {
            wp_die('Invalid group ID');
        }

        // Handle form submission
        if (isset($_POST['submit']) && wp_verify_nonce($_POST['_wpnonce'], 'edit_group_' . $group_id)) {
            $result = DakoiiSlideshow::update_group(
                $group_id,
                sanitize_text_field($_POST['name']),
                sanitize_textarea_field($_POST['description']),
                sanitize_text_field($_POST['tags'])
            );

            if ($result !== false) {
                echo '<div class="notice notice-success"><p>Group updated successfully!</p></div>';
            } else {
                echo '<div class="notice notice-error"><p>Error updating group.</p></div>';
            }
        }

        $group = DakoiiSlideshow::get_group($group_id);

        if (!$group) {
            wp_die('Group not found');
        }
        ?>
        <div class="wrap">
            <h1>Edit Slideshow Group</h1>

            <form method="post" action="">
                <?php wp_nonce_field('edit_group_' . $group_id); ?>
                <table class="form-table">
                    <tr>
                        <th><label for="name">Group Name</label></th>
                        <td><input type="text" id="name" name="name" value="<?php echo esc_attr($group->name); ?>" required class="regular-text"></td>
                    </tr>
                    <tr>
                        <th><label for="description">Description</label></th>
                        <td><textarea id="description" name="description" class="large-text" rows="5"><?php echo esc_textarea($group->description); ?></textarea></td>
                    </tr>
                    <tr>
                        <th><label for="tags">Tags</label></th>
                        <td>
                            <input type="text" id="tags" name="tags" value="<?php echo esc_attr($group->tags); ?>" class="regular-text" placeholder="home, about, contact">
                            <p class="description">Comma-separated tags to identify where this slideshow should appear.</p>
                        </td>
                    </tr>
                </table>

                <p class="submit">
                    <input type="submit" name="submit" class="button-primary" value="Update Group">
                    <a href="<?php echo admin_url('admin.php?page=dakoii-slideshows'); ?>" class="button">Cancel</a>
                </p>
            </form>
        </div>
        <?php
    }

    public function manage_slides_page() {
        $group_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

        if (!$group_id) {
            wp_die('Invalid group ID');
        }

        $group = DakoiiSlideshow::get_group($group_id);

        if (!$group) {
            wp_die('Group not found');
        }

        // Handle form submission for adding new slide
        if (isset($_POST['add_slide']) && wp_verify_nonce($_POST['_wpnonce'], 'add_slide_' . $group_id)) {
            $result = DakoiiSlideshow::add_slide(
                $group_id,
                sanitize_text_field($_POST['title']),
                sanitize_textarea_field($_POST['description']),
                esc_url_raw($_POST['image_url']),
                esc_url_raw($_POST['link_url']),
                intval($_POST['order'])
            );

            if ($result !== false) {
                echo '<div class="notice notice-success"><p>Slide added successfully!</p></div>';
            } else {
                echo '<div class="notice notice-error"><p>Error adding slide.</p></div>';
            }
        }

        // Handle slide deletion
        if (isset($_GET['delete_slide']) && wp_verify_nonce($_GET['_wpnonce'], 'delete_slide')) {
            $slide_id = intval($_GET['delete_slide']);
            $result = DakoiiSlideshow::delete_slide($slide_id);

            if ($result !== false) {
                echo '<div class="notice notice-success"><p>Slide deleted successfully!</p></div>';
            } else {
                echo '<div class="notice notice-error"><p>Error deleting slide.</p></div>';
            }
        }

        $slides = DakoiiSlideshow::get_slides($group_id);
        ?>
        <div class="wrap">
            <h1>Manage Slides: <?php echo esc_html($group->name); ?></h1>

            <div class="tablenav top">
                <div class="alignleft actions">
                    <a href="<?php echo admin_url('admin.php?page=dakoii-slideshows'); ?>" class="button">← Back to Groups</a>
                </div>
            </div>

            <?php if ($slides): ?>
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th>Image</th>
                            <th>Title</th>
                            <th>Description</th>
                            <th>Link</th>
                            <th>Order</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($slides as $slide): ?>
                            <tr>
                                <td>
                                    <?php if ($slide->image_url): ?>
                                        <img src="<?php echo esc_url($slide->image_url); ?>" alt="<?php echo esc_attr($slide->title); ?>" style="width: 80px; height: 60px; object-fit: cover;">
                                    <?php else: ?>
                                        <div style="width: 80px; height: 60px; background: #f0f0f0; display: flex; align-items: center; justify-content: center; font-size: 12px; color: #666;">No Image</div>
                                    <?php endif; ?>
                                </td>
                                <td><strong><?php echo esc_html($slide->title ?: 'Untitled'); ?></strong></td>
                                <td><?php echo esc_html($slide->description); ?></td>
                                <td>
                                    <?php if ($slide->link_url): ?>
                                        <a href="<?php echo esc_url($slide->link_url); ?>" target="_blank"><?php echo esc_html($slide->link_url); ?></a>
                                    <?php else: ?>
                                        —
                                    <?php endif; ?>
                                </td>
                                <td><?php echo intval($slide->slide_order); ?></td>
                                <td>
                                    <a href="<?php echo wp_nonce_url(admin_url('admin.php?page=dakoii-slideshows-manage&id=' . $group_id . '&delete_slide=' . $slide->id), 'delete_slide'); ?>"
                                       onclick="return confirm('Are you sure you want to delete this slide?');"
                                       style="color: #a00;">Delete</a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php else: ?>
                <p>No slides found. Add your first slide below.</p>
            <?php endif; ?>

            <hr>
            <h2>Add New Slide</h2>

            <form method="post" action="">
                <?php wp_nonce_field('add_slide_' . $group_id); ?>
                <table class="form-table">
                    <tr>
                        <th><label for="title">Title</label></th>
                        <td><input type="text" id="title" name="title" class="regular-text"></td>
                    </tr>
                    <tr>
                        <th><label for="description">Description</label></th>
                        <td><textarea id="description" name="description" class="large-text" rows="3"></textarea></td>
                    </tr>
                    <tr>
                        <th><label for="image_url">Image URL</label></th>
                        <td>
                            <input type="url" id="image_url" name="image_url" class="regular-text" required>
                            <button type="button" class="button" id="select-image">Select Image</button>
                        </td>
                    </tr>
                    <tr>
                        <th><label for="link_url">Link URL</label></th>
                        <td><input type="url" id="link_url" name="link_url" class="regular-text"></td>
                    </tr>
                    <tr>
                        <th><label for="order">Order</label></th>
                        <td><input type="number" id="order" name="order" class="small-text" value="0"></td>
                    </tr>
                </table>

                <p class="submit">
                    <input type="submit" name="add_slide" class="button-primary" value="Add Slide">
                </p>
            </form>
        </div>
        <?php
    }

    public function handle_ajax_requests() {
        error_log('Dakoii Slideshow: AJAX request received');
        error_log('Dakoii Slideshow: POST data: ' . print_r($_POST, true));

        // Verify nonce first
        if (!check_ajax_referer('dakoii_slideshow_nonce', 'nonce', false)) {
            error_log('Dakoii Slideshow: Nonce verification failed');
            wp_send_json_error('Security check failed');
            return;
        }
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Unauthorized access');
            return;
        }
        
        if (!isset($_POST['slideshow_action'])) {
            wp_send_json_error('No action specified');
            return;
        }
        
        $action = sanitize_text_field($_POST['slideshow_action']);
        
        try {
            switch ($action) {
                case 'create_group':
                    if (empty($_POST['name'])) {
                        wp_send_json_error('Group name is required');
                        return;
                    }
                    $result = DakoiiSlideshow::create_group(
                        $_POST['name'],
                        $_POST['description'] ?? '',
                        $_POST['tags'] ?? ''
                    );
                    if ($result !== false) {
                        wp_send_json_success('Group created successfully');
                    } else {
                        wp_send_json_error('Failed to create group');
                    }
                    break;
                    
                case 'update_group':
                    if (empty($_POST['group_id']) || empty($_POST['name'])) {
                        wp_send_json_error('Group ID and name are required');
                        return;
                    }
                    $result = DakoiiSlideshow::update_group(
                        intval($_POST['group_id']),
                        $_POST['name'],
                        $_POST['description'] ?? '',
                        $_POST['tags'] ?? ''
                    );
                    if ($result !== false) {
                        wp_send_json_success('Group updated successfully');
                    } else {
                        wp_send_json_error('Failed to update group');
                    }
                    break;
                    
                case 'delete_group':
                    if (empty($_POST['group_id'])) {
                        wp_send_json_error('Group ID is required');
                        return;
                    }
                    $result = DakoiiSlideshow::delete_group(intval($_POST['group_id']));
                    if ($result !== false) {
                        wp_send_json_success('Group deleted successfully');
                    } else {
                        wp_send_json_error('Failed to delete group');
                    }
                    break;
                    
                case 'get_group':
                    if (empty($_POST['group_id'])) {
                        wp_send_json_error('Group ID is required');
                        return;
                    }
                    $group = DakoiiSlideshow::get_group(intval($_POST['group_id']));
                    if ($group) {
                        wp_send_json_success($group);
                    } else {
                        wp_send_json_error('Group not found');
                    }
                    break;
                    
                case 'add_slide':
                    if (empty($_POST['group_id']) || empty($_POST['image_url'])) {
                        wp_send_json_error('Group ID and image URL are required');
                        return;
                    }
                    $result = DakoiiSlideshow::add_slide(
                        intval($_POST['group_id']),
                        $_POST['title'] ?? '',
                        $_POST['description'] ?? '',
                        $_POST['image_url'],
                        $_POST['link_url'] ?? '',
                        intval($_POST['order'] ?? 0)
                    );
                    if ($result !== false) {
                        wp_send_json_success('Slide added successfully');
                    } else {
                        wp_send_json_error('Failed to add slide');
                    }
                    break;
                    
                case 'get_slides':
                    if (empty($_POST['group_id'])) {
                        wp_send_json_error('Group ID is required');
                        return;
                    }
                    $slides = DakoiiSlideshow::get_slides(intval($_POST['group_id']));
                    wp_send_json_success($slides);
                    break;
                    
                case 'delete_slide':
                    if (empty($_POST['slide_id'])) {
                        wp_send_json_error('Slide ID is required');
                        return;
                    }
                    $result = DakoiiSlideshow::delete_slide(intval($_POST['slide_id']));
                    if ($result !== false) {
                        wp_send_json_success('Slide deleted successfully');
                    } else {
                        wp_send_json_error('Failed to delete slide');
                    }
                    break;
                    
                default:
                    wp_send_json_error('Invalid action: ' . $action);
            }
        } catch (Exception $e) {
            error_log('Dakoii Slideshow Error: ' . $e->getMessage());
            wp_send_json_error('An error occurred. Please try again.');
        }
        
        wp_die();
    }
}